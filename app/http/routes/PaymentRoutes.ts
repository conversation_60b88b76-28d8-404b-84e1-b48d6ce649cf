import { singleton } from 'tsyringe';

import { searchPaymentsSchema } from '../@types/cli-api/payments/searchPayments/schema';
import { SearchPaymentsController } from '../controllers/payments/SearchPaymentsController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class PaymentRoutes {
  constructor(
    private readonly searchPaymentsController: SearchPaymentsController,
  ) { }

  async setup(server: FastifyInstance): Promise<void> {
    server.get('/', {
      schema: {
        tags: ['Payments'],
        description: 'Endpoint to search payments',
        ...searchPaymentsSchema,
      },
      handler: this.searchPaymentsController.handler.bind(this.searchPaymentsController),
    });
  }
}
